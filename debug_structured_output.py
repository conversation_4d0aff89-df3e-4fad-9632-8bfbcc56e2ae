import asyncio
import logging
from pydantic import BaseModel, Field
from litellm import Router
from langchain_litellm import ChatLiteLLMRouter

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义结构化输出模型
class ResponseFormatter(BaseModel):
    """Always use this tool to structure your response to the user."""
    answer: str = Field(description="The answer to the user's question")
    followup_question: str = Field(description="A followup question the user could ask")


def create_model():
    """创建配置好的模型"""
    print("正在创建模型...")
    
    model_list = [
        {
            "model_name": "Qwen3-30B-A3B",
            "litellm_params": {
                "model": "openai/Qwen3-30B-A3B",
                "api_key": "sk-Yy9EXhOgWMCa4Xq9dnlW_Q",
                "api_base": "http://mogoseekapi.zhidaoauto.com",
                "timeout": 120,
                "max_retries": 2,
            },
        }
    ]
    
    try:
        litellm_router = Router(
            model_list=model_list,
            timeout=120,
            num_retries=2,
        )
        model = ChatLiteLLMRouter(
            router=litellm_router, 
            model_name="Qwen3-30B-A3B", 
            temperature=0.1,
            request_timeout=120,
        )
        print("模型创建成功!")
        return model
    except Exception as e:
        print(f"创建模型时出错: {e}")
        raise


async def test_basic_call():
    """测试基本API调用"""
    print("\n=== 测试基本API调用 ===")
    
    try:
        model = create_model()
        print("开始基本API调用...")
        
        # 使用简单的同步调用
        from langchain_core.messages import HumanMessage
        
        messages = [HumanMessage(content="你好，请简单回复")]
        result = await model.ainvoke(messages)
        
        print(f"基本调用成功!")
        print(f"响应内容: {result.content}")
        return True
        
    except Exception as e:
        print(f"基本API调用失败: {e}")
        return False


async def test_structured_output_simple():
    """测试简单的结构化输出"""
    print("\n=== 测试结构化输出 ===")
    
    try:
        model = create_model()
        
        # 使用工具调用方式实现结构化输出
        print("尝试使用工具调用方式...")
        
        # 将ResponseFormatter作为工具绑定
        model_with_tools = model.bind_tools([ResponseFormatter])
        
        from langchain_core.messages import HumanMessage
        messages = [HumanMessage(content="什么是AI？请使用ResponseFormatter工具来回答")]
        
        print("调用带工具的模型...")
        result = await model_with_tools.ainvoke(messages)
        
        print(f"工具调用结果: {result}")
        
        if hasattr(result, 'tool_calls') and result.tool_calls:
            tool_call = result.tool_calls[0]
            print(f"工具调用参数: {tool_call['args']}")
            
            # 解析为Pydantic对象
            structured_result = ResponseFormatter(**tool_call['args'])
            print(f"结构化回答: {structured_result.answer}")
            print(f"后续问题: {structured_result.followup_question}")
            return True
        else:
            print("没有工具调用结果")
            return False
            
    except Exception as e:
        print(f"结构化输出测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_with_structured_output():
    """测试with_structured_output方法"""
    print("\n=== 测试with_structured_output方法 ===")
    
    try:
        model = create_model()
        
        print("使用with_structured_output方法...")
        structured_model = model.with_structured_output(ResponseFormatter)
        
        print("调用结构化模型...")
        result = await structured_model.ainvoke("什么是机器学习？")
        
        print(f"结构化输出成功!")
        print(f"回答: {result.answer}")
        print(f"后续问题: {result.followup_question}")
        print(f"数据类型: {type(result)}")
        
        return True
        
    except Exception as e:
        print(f"with_structured_output方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主测试函数"""
    print("开始调试结构化输出...")
    
    # 步骤1: 测试基本API调用
    basic_success = await test_basic_call()
    
    if not basic_success:
        print("基本API调用失败，停止测试")
        return
    
    # 步骤2: 测试工具调用方式的结构化输出
    tool_success = await test_structured_output_simple()
    
    # 步骤3: 测试with_structured_output方法
    if tool_success:
        print("\n工具调用方式成功，现在测试with_structured_output方法...")
        await test_with_structured_output()
    else:
        print("\n工具调用方式失败，跳过with_structured_output测试")


if __name__ == "__main__":
    asyncio.run(main())
