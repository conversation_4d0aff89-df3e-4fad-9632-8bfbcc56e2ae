# Pydantic 模型详解

## 什么是 Pydantic？

Pydantic 是一个 Python 库，用于**数据验证**和**设置管理**。它使用 Python 的类型注解来验证数据，确保数据符合预期的类型和格式。

简单来说，Pydantic 让你可以：
1. **定义数据结构** - 像定义类一样定义数据的"形状"
2. **自动验证** - 确保数据符合你定义的规则
3. **类型转换** - 自动将数据转换为正确的类型
4. **生成文档** - 自动生成数据结构的文档

## 为什么需要 Pydantic？

### 传统方式的问题
```python
# 传统的字典方式 - 容易出错
user_data = {
    "name": "张三",
    "age": "25",  # 这里应该是数字，但传入了字符串
    "email": "invalid-email"  # 这不是有效的邮箱格式
}

# 没有验证，可能导致运行时错误
print(user_data["age"] + 1)  # TypeError: can only concatenate str (not "int") to str
```

### Pydantic 的解决方案
```python
from pydantic import BaseModel, Field, EmailStr

class User(BaseModel):
    name: str
    age: int
    email: EmailStr

# 自动验证和类型转换
user = User(name="张三", age="25", email="<EMAIL>")
print(user.age + 1)  # 26 - 自动转换为整数
```

## Pydantic 基础概念

### 1. BaseModel 基类
所有 Pydantic 模型都继承自 `BaseModel`：

```python
from pydantic import BaseModel

class Person(BaseModel):
    name: str
    age: int
```

### 2. 类型注解
使用 Python 的类型注解定义字段类型：

```python
from typing import List, Optional, Dict
from pydantic import BaseModel

class Student(BaseModel):
    name: str                    # 必需的字符串
    age: int                     # 必需的整数
    grades: List[float]          # 浮点数列表
    address: Optional[str] = None # 可选字符串，默认为None
    metadata: Dict[str, str]     # 字符串到字符串的字典
```

### 3. Field 字段定义
使用 `Field` 添加更多约束和描述：

```python
from pydantic import BaseModel, Field

class Product(BaseModel):
    name: str = Field(description="产品名称", min_length=1, max_length=100)
    price: float = Field(description="价格", gt=0, le=10000)  # 大于0，小于等于10000
    quantity: int = Field(description="库存数量", ge=0)        # 大于等于0
    tags: List[str] = Field(description="标签", max_items=10)
```

## 常用的验证规则

### 数值约束
```python
class NumberValidation(BaseModel):
    positive_int: int = Field(gt=0)           # 大于0
    percentage: float = Field(ge=0, le=100)   # 0到100之间
    even_number: int = Field(multiple_of=2)   # 必须是2的倍数
```

### 字符串约束
```python
class StringValidation(BaseModel):
    username: str = Field(min_length=3, max_length=20)
    password: str = Field(regex=r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,}$')
    phone: str = Field(regex=r'^\d{11}$')  # 11位数字
```

### 集合约束
```python
class CollectionValidation(BaseModel):
    tags: List[str] = Field(min_items=1, max_items=5)
    scores: List[int] = Field(min_items=3, max_items=3)  # 恰好3个元素
```

## 实际应用示例

### 1. 用户信息模型
```python
from pydantic import BaseModel, Field, EmailStr
from typing import Optional
from datetime import datetime

class UserProfile(BaseModel):
    """用户档案模型"""
    id: int = Field(description="用户ID", gt=0)
    username: str = Field(description="用户名", min_length=3, max_length=20)
    email: EmailStr = Field(description="邮箱地址")
    full_name: str = Field(description="全名", min_length=1)
    age: Optional[int] = Field(None, description="年龄", ge=0, le=150)
    is_active: bool = Field(True, description="是否激活")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    
    class Config:
        # 配置示例
        json_encoders = {
            datetime: lambda v: v.isoformat()
        }
```

### 2. API 响应模型
```python
from typing import List, Optional
from pydantic import BaseModel, Field

class APIResponse(BaseModel):
    """标准API响应格式"""
    success: bool = Field(description="请求是否成功")
    message: str = Field(description="响应消息")
    data: Optional[dict] = Field(None, description="响应数据")
    errors: Optional[List[str]] = Field(None, description="错误列表")
    timestamp: datetime = Field(default_factory=datetime.now, description="响应时间")

class PaginatedResponse(BaseModel):
    """分页响应格式"""
    items: List[dict] = Field(description="数据项列表")
    total: int = Field(description="总数量", ge=0)
    page: int = Field(description="当前页码", ge=1)
    page_size: int = Field(description="每页大小", ge=1, le=100)
    has_next: bool = Field(description="是否有下一页")
```

### 3. 配置管理
```python
from pydantic import BaseSettings, Field

class DatabaseConfig(BaseSettings):
    """数据库配置"""
    host: str = Field("localhost", description="数据库主机")
    port: int = Field(5432, description="数据库端口")
    username: str = Field(description="用户名")
    password: str = Field(description="密码")
    database: str = Field(description="数据库名")
    
    class Config:
        env_prefix = "DB_"  # 从环境变量 DB_HOST, DB_PORT 等读取
```

## Pydantic 的强大功能

### 1. 自动类型转换
```python
class AutoConversion(BaseModel):
    number: int
    flag: bool
    items: List[str]

# 自动转换
data = AutoConversion(
    number="123",        # 字符串转整数
    flag="true",         # 字符串转布尔值
    items="a,b,c"        # 字符串转列表（需要自定义验证器）
)
```

### 2. 嵌套模型
```python
class Address(BaseModel):
    street: str
    city: str
    country: str

class Company(BaseModel):
    name: str
    address: Address  # 嵌套模型

class Employee(BaseModel):
    name: str
    company: Company  # 多层嵌套
```

### 3. 自定义验证器
```python
from pydantic import BaseModel, validator

class CustomValidation(BaseModel):
    name: str
    age: int
    
    @validator('name')
    def name_must_not_be_empty(cls, v):
        if not v.strip():
            raise ValueError('姓名不能为空')
        return v.title()  # 转换为标题格式
    
    @validator('age')
    def age_must_be_reasonable(cls, v):
        if v < 0 or v > 150:
            raise ValueError('年龄必须在0-150之间')
        return v
```

## 与结构化输出的关系

在结构化输出中，Pydantic 模型扮演了**数据契约**的角色：

1. **定义期望的输出格式** - 告诉AI模型应该返回什么样的数据
2. **自动验证AI输出** - 确保AI返回的数据符合预期
3. **类型安全** - 提供编译时和运行时的类型检查
4. **文档生成** - 自动生成JSON Schema供AI模型理解

```python
# 这个模型定义了AI应该如何回答问题
class AIResponse(BaseModel):
    answer: str = Field(description="对用户问题的详细回答")
    confidence: float = Field(description="回答的置信度", ge=0, le=1)
    sources: List[str] = Field(description="信息来源", max_items=5)
    follow_up: Optional[str] = Field(None, description="建议的后续问题")

# AI模型会被指示按照这个格式返回数据
# 返回的数据会自动验证是否符合这个模型
```

## 总结

Pydantic 模型本质上是：
- **数据的蓝图** - 定义数据应该是什么样子
- **验证器** - 确保数据符合规则
- **类型转换器** - 自动处理类型转换
- **文档生成器** - 自动生成数据结构文档

在结构化输出的上下文中，Pydantic 模型就像是给AI模型的"作业要求"，告诉它应该返回什么格式的数据，并且自动检查AI是否按要求完成了"作业"。
