import asyncio
import json
from typing import List, Optional
from datetime import datetime
from pydantic import BaseModel, Field

from langgraph.prebuilt import create_react_agent
from langchain_mcp_adapters.client import MultiServerMCPClient
from langgraph.checkpoint.memory import InMemorySaver

from litellm import Router
from langchain_litellm import ChatLiteLLMRouter


# 定义结构化输出的Pydantic模型
class ResponseFormatter(BaseModel):
    """Always use this tool to structure your response to the user."""
    answer: str = Field(description="The answer to the user's question")
    followup_question: str = Field(description="A followup question the user could ask")


class AnalysisResult(BaseModel):
    """用于分析结果的结构化输出"""
    summary: str = Field(description="分析摘要")
    key_points: List[str] = Field(description="关键要点列表")
    confidence_score: float = Field(description="置信度分数 (0-1)", ge=0, le=1)
    timestamp: datetime = Field(default_factory=datetime.now, description="分析时间")


class TaskPlan(BaseModel):
    """任务规划的结构化输出"""
    task_name: str = Field(description="任务名称")
    steps: List[str] = Field(description="执行步骤列表")
    estimated_time: int = Field(description="预估时间（分钟）")
    priority: str = Field(description="优先级", pattern="^(高|中|低)$")


def create_model_with_tools():
    """创建配置好的模型和工具"""
    model_list = [
        {
            "model_name": "Qwen3-30B-A3B",
            "litellm_params": {
                "model": "openai/Qwen3-30B-A3B",
                "api_key": "sk-Yy9EXhOgWMCa4Xq9dnlW_Q",
                "api_base": "http://mogoseekapi.zhidaoauto.com",
            },
        }
    ]
    
    litellm_router = Router(model_list=model_list)
    model = ChatLiteLLMRouter(
        router=litellm_router, 
        model_name="Qwen3-30B-A3B", 
        temperature=0.1
    )
    
    # 这里可以添加其他工具
    tools = []
    
    return model, tools


async def example_basic_structured_output():
    """基础结构化输出示例"""
    print("=== 基础结构化输出示例 ===")
    
    model, tools = create_model_with_tools()
    
    # 使用with_structured_output方法
    structured_model = model.with_structured_output(ResponseFormatter)
    
    # 调用模型
    user_question = "什么是人工智能？"
    result = await structured_model.ainvoke(user_question)
    
    print(f"用户问题: {user_question}")
    print(f"结构化回答: {result.answer}")
    print(f"后续问题: {result.followup_question}")
    print(f"结果类型: {type(result)}")
    
    return result


async def example_analysis_structured_output():
    """分析结果结构化输出示例"""
    print("\n=== 分析结果结构化输出示例 ===")
    
    model, tools = create_model_with_tools()
    
    # 使用分析结果模型
    analysis_model = model.with_structured_output(AnalysisResult)
    
    # 分析请求
    analysis_request = """
    请分析以下文本的主要内容：
    "机器学习是人工智能的一个重要分支，它通过算法让计算机能够从数据中学习并做出预测。
    深度学习作为机器学习的子集，使用神经网络来模拟人脑的学习过程。"
    """
    
    result = await analysis_model.ainvoke(analysis_request)
    
    print(f"分析摘要: {result.summary}")
    print(f"关键要点: {result.key_points}")
    print(f"置信度: {result.confidence_score}")
    print(f"分析时间: {result.timestamp}")
    
    return result


async def example_task_planning_output():
    """任务规划结构化输出示例"""
    print("\n=== 任务规划结构化输出示例 ===")
    
    model, tools = create_model_with_tools()
    
    # 使用任务规划模型
    planning_model = model.with_structured_output(TaskPlan)
    
    # 规划请求
    planning_request = "请为'学习Python编程'制定一个详细的学习计划"
    
    result = await planning_model.ainvoke(planning_request)
    
    print(f"任务名称: {result.task_name}")
    print(f"执行步骤:")
    for i, step in enumerate(result.steps, 1):
        print(f"  {i}. {step}")
    print(f"预估时间: {result.estimated_time} 分钟")
    print(f"优先级: {result.priority}")
    
    return result


async def example_with_langgraph_agent():
    """结合LangGraph代理的结构化输出示例"""
    print("\n=== LangGraph代理结构化输出示例 ===")
    
    model, tools = create_model_with_tools()
    
    # 创建内存保存器
    memory = InMemorySaver()
    
    # 创建结构化输出模型
    structured_model = model.with_structured_output(ResponseFormatter)
    
    # 创建代理（如果有工具的话）
    if tools:
        agent = create_react_agent(structured_model, tools, checkpointer=memory)
        
        # 配置
        config = {"configurable": {"thread_id": "example-thread"}}
        
        # 运行代理
        user_input = "请解释什么是区块链技术"
        
        async for chunk in agent.astream(
            {"messages": [("user", user_input)]}, 
            config
        ):
            if "agent" in chunk:
                print(chunk["agent"]["messages"][-1].content)
    else:
        # 直接使用结构化模型
        user_input = "请解释什么是区块链技术"
        result = await structured_model.ainvoke(user_input)
        
        print(f"用户问题: {user_input}")
        print(f"结构化回答: {result.answer}")
        print(f"后续问题: {result.followup_question}")


def example_json_serialization():
    """JSON序列化示例"""
    print("\n=== JSON序列化示例 ===")
    
    # 创建示例数据
    response = ResponseFormatter(
        answer="人工智能是模拟人类智能的计算机系统",
        followup_question="人工智能有哪些主要应用领域？"
    )
    
    # 转换为JSON
    json_output = response.model_dump_json(indent=2)
    print("JSON格式输出:")
    print(json_output)
    
    # 从JSON恢复
    json_data = json.loads(json_output)
    restored_response = ResponseFormatter(**json_data)
    print(f"\n从JSON恢复的对象: {restored_response}")


async def main():
    """主函数，运行所有示例"""
    try:
        # 运行各种结构化输出示例
        await example_basic_structured_output()
        await example_analysis_structured_output()
        await example_task_planning_output()
        await example_with_langgraph_agent()
        
        # JSON序列化示例（同步）
        example_json_serialization()
        
    except Exception as e:
        print(f"运行示例时出错: {e}")


if __name__ == "__main__":
    # 运行异步主函数
    asyncio.run(main())
