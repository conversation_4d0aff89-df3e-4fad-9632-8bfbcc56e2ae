"""
结构化输出原理演示
这个文件展示了结构化输出的核心概念和实现原理
"""

import json
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ValidationError
from enum import Enum

# ============================================================================
# 1. 定义结构化输出模型
# ============================================================================

class Priority(str, Enum):
    HIGH = "高"
    MEDIUM = "中"
    LOW = "低"

class ResponseFormatter(BaseModel):
    """基础响应格式化器"""
    answer: str = Field(description="对用户问题的回答")
    followup_question: str = Field(description="用户可能感兴趣的后续问题")

class TaskAnalysis(BaseModel):
    """任务分析结构"""
    task_name: str = Field(description="任务名称")
    steps: List[str] = Field(description="执行步骤", min_items=1, max_items=10)
    estimated_time: int = Field(description="预估时间(分钟)", ge=1, le=1440)
    priority: Priority = Field(description="优先级")
    dependencies: Optional[List[str]] = Field(default=None, description="依赖项")

class DataExtraction(BaseModel):
    """数据提取结构"""
    entities: List[str] = Field(description="提取的实体")
    keywords: List[str] = Field(description="关键词", max_items=10)
    sentiment: float = Field(description="情感分数", ge=-1.0, le=1.0)
    confidence: float = Field(description="置信度", ge=0.0, le=1.0)
    metadata: Optional[Dict[str, Any]] = Field(default=None, description="元数据")

# ============================================================================
# 2. 模拟工具调用转换
# ============================================================================

def pydantic_to_tool_definition(pydantic_class: type[BaseModel]) -> dict:
    """
    将Pydantic模型转换为工具定义
    这模拟了LangChain内部的转换过程
    """
    schema = pydantic_class.model_json_schema()
    
    tool_definition = {
        "type": "function",
        "function": {
            "name": pydantic_class.__name__,
            "description": pydantic_class.__doc__ or f"Use this tool to structure response as {pydantic_class.__name__}",
            "parameters": {
                "type": "object",
                "properties": schema.get("properties", {}),
                "required": schema.get("required", [])
            }
        }
    }
    
    return tool_definition

def pydantic_to_json_schema(pydantic_class: type[BaseModel]) -> dict:
    """
    将Pydantic模型转换为JSON Schema
    这模拟了JSON模式的生成过程
    """
    return pydantic_class.model_json_schema()

# ============================================================================
# 3. 模拟API调用和响应解析
# ============================================================================

def simulate_tool_call_response(tool_definition: dict, user_input: str) -> dict:
    """
    模拟工具调用的API响应
    在实际情况下，这是语言模型的响应
    """
    # 这里我们手动创建一个模拟响应
    if "ResponseFormatter" in tool_definition["function"]["name"]:
        return {
            "tool_calls": [
                {
                    "id": "call_123",
                    "type": "function",
                    "function": {
                        "name": "ResponseFormatter",
                        "arguments": json.dumps({
                            "answer": f"这是对'{user_input}'的回答",
                            "followup_question": "您还想了解什么相关信息？"
                        })
                    }
                }
            ]
        }
    elif "TaskAnalysis" in tool_definition["function"]["name"]:
        return {
            "tool_calls": [
                {
                    "id": "call_456",
                    "type": "function", 
                    "function": {
                        "name": "TaskAnalysis",
                        "arguments": json.dumps({
                            "task_name": f"分析任务: {user_input}",
                            "steps": ["步骤1: 需求分析", "步骤2: 方案设计", "步骤3: 实施执行"],
                            "estimated_time": 120,
                            "priority": "中",
                            "dependencies": ["前置条件A", "前置条件B"]
                        })
                    }
                }
            ]
        }
    
    return {"tool_calls": []}

def parse_tool_call_response(response: dict, pydantic_class: type[BaseModel]):
    """
    解析工具调用响应并转换为Pydantic对象
    这模拟了LangChain的响应解析过程
    """
    if not response.get("tool_calls"):
        raise ValueError("No tool calls found in response")
    
    tool_call = response["tool_calls"][0]
    arguments = json.loads(tool_call["function"]["arguments"])
    
    try:
        # 创建并验证Pydantic对象
        return pydantic_class(**arguments)
    except ValidationError as e:
        raise ValueError(f"Validation failed: {e}")

# ============================================================================
# 4. 演示函数
# ============================================================================

def demo_basic_structured_output():
    """演示基础结构化输出"""
    print("=== 基础结构化输出演示 ===")
    
    # 1. 定义模型
    print("1. 定义Pydantic模型:")
    print(f"   类名: {ResponseFormatter.__name__}")
    print(f"   字段: {list(ResponseFormatter.model_fields.keys())}")
    
    # 2. 转换为工具定义
    tool_def = pydantic_to_tool_definition(ResponseFormatter)
    print("\n2. 转换为工具定义:")
    print(json.dumps(tool_def, indent=2, ensure_ascii=False))
    
    # 3. 模拟API调用
    user_input = "什么是人工智能？"
    response = simulate_tool_call_response(tool_def, user_input)
    print(f"\n3. 模拟API响应 (用户输入: {user_input}):")
    print(json.dumps(response, indent=2, ensure_ascii=False))
    
    # 4. 解析响应
    structured_result = parse_tool_call_response(response, ResponseFormatter)
    print("\n4. 解析后的结构化对象:")
    print(f"   类型: {type(structured_result)}")
    print(f"   答案: {structured_result.answer}")
    print(f"   后续问题: {structured_result.followup_question}")

def demo_complex_structured_output():
    """演示复杂结构化输出"""
    print("\n=== 复杂结构化输出演示 ===")
    
    # 1. 复杂模型
    print("1. 复杂Pydantic模型:")
    print(f"   类名: {TaskAnalysis.__name__}")
    print(f"   字段: {list(TaskAnalysis.model_fields.keys())}")
    
    # 2. JSON Schema
    schema = pydantic_to_json_schema(TaskAnalysis)
    print("\n2. 生成的JSON Schema:")
    print(json.dumps(schema, indent=2, ensure_ascii=False))
    
    # 3. 模拟复杂响应
    tool_def = pydantic_to_tool_definition(TaskAnalysis)
    response = simulate_tool_call_response(tool_def, "学习Python编程")
    structured_result = parse_tool_call_response(response, TaskAnalysis)
    
    print("\n3. 解析后的复杂结构化对象:")
    print(f"   任务名称: {structured_result.task_name}")
    print(f"   执行步骤: {structured_result.steps}")
    print(f"   预估时间: {structured_result.estimated_time}分钟")
    print(f"   优先级: {structured_result.priority}")
    print(f"   依赖项: {structured_result.dependencies}")

def demo_validation():
    """演示数据验证"""
    print("\n=== 数据验证演示 ===")
    
    # 正确的数据
    print("1. 正确数据验证:")
    try:
        valid_data = DataExtraction(
            entities=["Python", "编程"],
            keywords=["学习", "教程"],
            sentiment=0.8,
            confidence=0.9
        )
        print(f"   ✅ 验证成功: {valid_data}")
    except ValidationError as e:
        print(f"   ❌ 验证失败: {e}")
    
    # 错误的数据
    print("\n2. 错误数据验证:")
    try:
        invalid_data = DataExtraction(
            entities=["Python"],
            keywords=["学习"],
            sentiment=2.0,  # 超出范围
            confidence=-0.5  # 超出范围
        )
        print(f"   ✅ 验证成功: {invalid_data}")
    except ValidationError as e:
        print(f"   ❌ 验证失败: {e}")

def main():
    """主演示函数"""
    print("🚀 结构化输出原理演示")
    print("=" * 60)
    
    demo_basic_structured_output()
    demo_complex_structured_output()
    demo_validation()
    
    print("\n" + "=" * 60)
    print("📝 总结:")
    print("1. Pydantic模型定义了数据结构和验证规则")
    print("2. 模型可以转换为工具定义或JSON Schema")
    print("3. 语言模型按照定义的格式生成响应")
    print("4. 响应被解析并验证为类型安全的对象")
    print("5. 这确保了数据的一致性和可靠性")

if __name__ == "__main__":
    main()
