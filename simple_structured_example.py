import asyncio
import logging
from pydantic import BaseModel, Field
from litellm import Router
from langchain_litellm import ChatLiteLLMRouter

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 定义结构化输出模型
class ResponseFormatter(BaseModel):
    """Always use this tool to structure your response to the user."""
    answer: str = Field(description="The answer to the user's question")
    followup_question: str = Field(description="A followup question the user could ask")


def create_model():
    """创建配置好的模型"""
    print("正在创建模型...")

    model_list = [
        {
            "model_name": "Qwen3-30B-A3B",
            "litellm_params": {
                "model": "openai/Qwen3-30B-A3B",
                "api_key": "sk-Yy9EXhOgWMCa4Xq9dnlW_Q",
                "api_base": "http://mogoseekapi.zhidaoauto.com",
                "timeout": 120,  # 增加超时时间到120秒
                "max_retries": 3,  # 增加重试次数
            },
        }
    ]

    try:
        litellm_router = Router(
            model_list=model_list,
            timeout=120,  # 路由器级别的超时设置
            num_retries=3,  # 路由器级别的重试设置
        )
        model = ChatLiteLLMRouter(
            router=litellm_router,
            model_name="Qwen3-30B-A3B",
            temperature=0.1,
            request_timeout=120,  # 请求超时
        )
        print("模型创建成功!")
        return model
    except Exception as e:
        print(f"创建模型时出错: {e}")
        raise


async def test_api_connection():
    """测试API连接"""
    print("正在测试API连接...")

    try:
        model = create_model()

        # 先测试基本调用（不使用结构化输出）
        print("测试基本API调用...")
        basic_result = await model.ainvoke("你好")
        print(f"基本调用成功: {basic_result.content[:100]}...")

        return True
    except Exception as e:
        print(f"API连接测试失败: {e}")
        return False


async def test_structured_output():
    """测试结构化输出"""
    print("开始测试结构化输出...")

    # 首先测试API连接
    if not await test_api_connection():
        print("API连接失败，无法继续测试结构化输出")
        return

    try:
        # 创建模型
        model = create_model()

        print("正在绑定结构化输出模式...")
        # 绑定结构化输出模式
        structured_model = model.with_structured_output(ResponseFormatter)
        print("结构化输出模式绑定成功!")

        # 测试问题（先用一个简单的问题）
        test_question = "什么是AI？"

        print(f"\n问题: {test_question}")
        print("-" * 50)

        print("正在调用结构化模型...")
        # 调用结构化模型
        result = await structured_model.ainvoke(test_question)

        # 输出结构化结果
        print(f"回答: {result.answer}")
        print(f"后续问题: {result.followup_question}")
        print(f"数据类型: {type(result)}")

        # 验证是否为Pydantic对象
        print(f"是否为ResponseFormatter实例: {isinstance(result, ResponseFormatter)}")

        # 如果第一个成功，继续测试其他问题
        other_questions = [
            "如何学习Python编程？",
            "区块链技术的优势是什么？"
        ]

        for question in other_questions:
            print(f"\n问题: {question}")
            print("-" * 50)

            try:
                result = await structured_model.ainvoke(question)
                print(f"回答: {result.answer}")
                print(f"后续问题: {result.followup_question}")

            except Exception as e:
                print(f"处理问题 '{question}' 时出错: {e}")

    except Exception as e:
        print(f"结构化输出测试失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    print("开始运行结构化输出测试...")
    asyncio.run(test_structured_output())
