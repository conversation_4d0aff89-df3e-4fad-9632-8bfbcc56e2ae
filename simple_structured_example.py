import asyncio
from pydantic import BaseModel, Field
from litellm import Router
from langchain_litellm import ChatLiteLLMRouter


# 定义结构化输出模型
class ResponseFormatter(BaseModel):
    """Always use this tool to structure your response to the user."""
    answer: str = Field(description="The answer to the user's question")
    followup_question: str = Field(description="A followup question the user could ask")


def create_model():
    """创建配置好的模型"""
    model_list = [
        {
            "model_name": "Qwen3-30B-A3B",
            "litellm_params": {
                "model": "openai/Qwen3-30B-A3B",
                "api_key": "sk-Yy9EXhOgWMCa4Xq9dnlW_Q",
                "api_base": "http://mogoseekapi.zhidaoauto.com",
            },
        }
    ]
    
    litellm_router = Router(model_list=model_list)
    model = ChatLiteLLMRouter(
        router=litellm_router, 
        model_name="Qwen3-30B-A3B", 
        temperature=0.1
    )
    
    return model


async def test_structured_output():
    """测试结构化输出"""
    # 创建模型
    model = create_model()
    
    # 绑定结构化输出模式
    structured_model = model.with_structured_output(ResponseFormatter)
    
    # 测试问题
    questions = [
        "什么是机器学习？",
        "如何学习Python编程？",
        "区块链技术的优势是什么？"
    ]
    
    for question in questions:
        print(f"\n问题: {question}")
        print("-" * 50)
        
        try:
            # 调用结构化模型
            result = await structured_model.ainvoke(question)
            
            # 输出结构化结果
            print(f"回答: {result.answer}")
            print(f"后续问题: {result.followup_question}")
            print(f"数据类型: {type(result)}")
            
            # 验证是否为Pydantic对象
            print(f"是否为ResponseFormatter实例: {isinstance(result, ResponseFormatter)}")
            
        except Exception as e:
            print(f"处理问题时出错: {e}")


if __name__ == "__main__":
    asyncio.run(test_structured_output())
