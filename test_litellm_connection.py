import asyncio
import logging
from litellm import Router
from langchain_litellm import ChatLiteLLMRouter
from langchain_core.messages import HumanMessage

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def create_model():
    """创建LiteLLM模型"""
    print("正在创建LiteLLM模型...")
    
    model_list = [
        {
            "model_name": "Qwen3-30B-A3B",
            "litellm_params": {
                "model": "openai/Qwen3-30B-A3B",
                "api_key": "sk-Yy9EXhOgWMCa4Xq9dnlW_Q",
                "api_base": "http://mogoseekapi.zhidaoauto.com",
                "timeout": 60,
            },
        }
    ]
    
    try:
        litellm_router = Router(model_list=model_list)
        model = ChatLiteLLMRouter(
            router=litellm_router, 
            model_name="Qwen3-30B-A3B", 
            temperature=0.1
        )
        print("✅ 模型创建成功!")
        return model
    except Exception as e:
        print(f"❌ 创建模型时出错: {e}")
        raise

async def test_simple_call():
    """测试简单的API调用"""
    print("\n=== 测试简单API调用 ===")
    
    try:
        model = create_model()
        
        # 创建简单消息
        message = HumanMessage(content="你好")
        print(f"发送消息: {message.content}")
        
        print("正在调用API...")
        result = await model.ainvoke([message])
        
        print("✅ API调用成功!")
        print(f"响应类型: {type(result)}")
        print(f"响应内容: {result.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ API调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_multiple_calls():
    """测试多次API调用"""
    print("\n=== 测试多次API调用 ===")
    
    try:
        model = create_model()
        
        test_messages = [
            "1+1等于多少？",
            "什么是Python？",
            "今天天气怎么样？"
        ]
        
        for i, msg_content in enumerate(test_messages, 1):
            print(f"\n第{i}次调用:")
            print(f"问题: {msg_content}")
            
            message = HumanMessage(content=msg_content)
            result = await model.ainvoke([message])
            
            print(f"回答: {result.content[:100]}...")  # 只显示前100个字符
            
        print("✅ 多次调用测试成功!")
        return True
        
    except Exception as e:
        print(f"❌ 多次调用测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_sync_call():
    """测试同步调用"""
    print("\n=== 测试同步调用 ===")
    
    try:
        model = create_model()
        
        message = HumanMessage(content="请简单介绍一下自己")
        print(f"发送消息: {message.content}")
        
        print("正在进行同步调用...")
        result = model.invoke([message])
        
        print("✅ 同步调用成功!")
        print(f"响应内容: {result.content}")
        
        return True
        
    except Exception as e:
        print(f"❌ 同步调用失败: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """主测试函数"""
    print("🚀 开始测试LiteLLM连接...")
    
    # 测试1: 简单异步调用
    print("\n" + "="*50)
    success1 = await test_simple_call()
    
    if not success1:
        print("❌ 基本连接失败，停止后续测试")
        return
    
    # 测试2: 多次调用
    print("\n" + "="*50)
    success2 = await test_multiple_calls()
    
    # 测试3: 同步调用
    print("\n" + "="*50)
    success3 = await test_sync_call()
    
    # 总结
    print("\n" + "="*50)
    print("📊 测试结果总结:")
    print(f"简单异步调用: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"多次调用测试: {'✅ 成功' if success2 else '❌ 失败'}")
    print(f"同步调用测试: {'✅ 成功' if success3 else '❌ 失败'}")
    
    if success1:
        print("\n🎉 LiteLLM连接测试成功! 可以进行结构化输出测试了。")
    else:
        print("\n⚠️  LiteLLM连接有问题，需要先解决连接问题。")

if __name__ == "__main__":
    asyncio.run(main())
