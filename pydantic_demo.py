"""
Pydantic 模型演示
展示 Pydantic 的各种功能和用法
"""

from pydantic import BaseModel, Field, validator, EmailStr, ValidationError
from typing import List, Optional, Dict, Union
from datetime import datetime
from enum import Enum
import json

# ============================================================================
# 1. 基础 Pydantic 模型
# ============================================================================

class Person(BaseModel):
    """最简单的 Pydantic 模型"""
    name: str
    age: int

def demo_basic_model():
    """演示基础模型功能"""
    print("=== 基础 Pydantic 模型演示 ===")
    
    # 正确创建
    person1 = Person(name="张三", age=25)
    print(f"✅ 正确创建: {person1}")
    print(f"   姓名: {person1.name}, 年龄: {person1.age}")
    
    # 自动类型转换
    person2 = Person(name="李四", age="30")  # 字符串自动转换为整数
    print(f"✅ 自动转换: {person2} (age从字符串'30'转换为整数30)")
    
    # 验证失败
    try:
        person3 = Person(name="王五", age="不是数字")
        print(f"✅ 创建成功: {person3}")
    except ValidationError as e:
        print(f"❌ 验证失败: {e}")

# ============================================================================
# 2. 带约束的模型
# ============================================================================

class Student(BaseModel):
    """带有字段约束的学生模型"""
    name: str = Field(min_length=2, max_length=50, description="学生姓名")
    age: int = Field(ge=6, le=100, description="学生年龄")
    grade: float = Field(ge=0, le=100, description="成绩")
    subjects: List[str] = Field(min_items=1, max_items=10, description="选修科目")
    is_active: bool = Field(True, description="是否在校")

def demo_field_constraints():
    """演示字段约束"""
    print("\n=== 字段约束演示 ===")
    
    # 正确的数据
    try:
        student1 = Student(
            name="小明",
            age=18,
            grade=85.5,
            subjects=["数学", "物理", "化学"]
        )
        print(f"✅ 正确创建: {student1}")
    except ValidationError as e:
        print(f"❌ 验证失败: {e}")
    
    # 违反约束的数据
    try:
        student2 = Student(
            name="A",  # 太短
            age=200,   # 超出范围
            grade=150, # 超出范围
            subjects=[] # 空列表
        )
        print(f"✅ 创建成功: {student2}")
    except ValidationError as e:
        print(f"❌ 验证失败:")
        for error in e.errors():
            print(f"   - {error['loc'][0]}: {error['msg']}")

# ============================================================================
# 3. 复杂数据类型
# ============================================================================

class Status(str, Enum):
    ACTIVE = "active"
    INACTIVE = "inactive"
    PENDING = "pending"

class Address(BaseModel):
    """地址模型"""
    street: str
    city: str
    country: str = "中国"

class User(BaseModel):
    """复杂用户模型"""
    id: int
    username: str = Field(min_length=3, max_length=20)
    email: str  # 在实际项目中可以用 EmailStr
    full_name: Optional[str] = None
    age: Optional[int] = Field(None, ge=0, le=150)
    address: Optional[Address] = None
    tags: List[str] = Field(default_factory=list)
    metadata: Dict[str, Union[str, int]] = Field(default_factory=dict)
    status: Status = Status.ACTIVE
    created_at: datetime = Field(default_factory=datetime.now)

def demo_complex_types():
    """演示复杂数据类型"""
    print("\n=== 复杂数据类型演示 ===")
    
    # 创建复杂对象
    user = User(
        id=1,
        username="zhangsan",
        email="<EMAIL>",
        full_name="张三",
        age=25,
        address=Address(
            street="中关村大街1号",
            city="北京"
        ),
        tags=["开发者", "Python"],
        metadata={"level": "senior", "score": 95},
        status=Status.ACTIVE
    )
    
    print(f"✅ 复杂对象创建成功:")
    print(f"   用户名: {user.username}")
    print(f"   地址: {user.address.city}, {user.address.street}")
    print(f"   标签: {user.tags}")
    print(f"   状态: {user.status}")
    print(f"   创建时间: {user.created_at}")

# ============================================================================
# 4. 自定义验证器
# ============================================================================

class Product(BaseModel):
    """带自定义验证器的产品模型"""
    name: str
    price: float
    category: str
    
    @validator('name')
    def name_must_not_be_empty(cls, v):
        """验证产品名称不能为空"""
        if not v or not v.strip():
            raise ValueError('产品名称不能为空')
        return v.strip().title()  # 清理空格并转换为标题格式
    
    @validator('price')
    def price_must_be_positive(cls, v):
        """验证价格必须为正数"""
        if v <= 0:
            raise ValueError('价格必须大于0')
        return round(v, 2)  # 保留两位小数
    
    @validator('category')
    def category_must_be_valid(cls, v):
        """验证类别必须在允许的列表中"""
        valid_categories = ['电子产品', '服装', '食品', '图书', '其他']
        if v not in valid_categories:
            raise ValueError(f'类别必须是以下之一: {valid_categories}')
        return v

def demo_custom_validators():
    """演示自定义验证器"""
    print("\n=== 自定义验证器演示 ===")
    
    # 正确的产品
    try:
        product1 = Product(
            name="  iphone 15  ",  # 会被清理和格式化
            price=6999.999,        # 会被四舍五入
            category="电子产品"
        )
        print(f"✅ 产品创建成功: {product1}")
        print(f"   格式化后的名称: '{product1.name}'")
        print(f"   四舍五入后的价格: {product1.price}")
    except ValidationError as e:
        print(f"❌ 验证失败: {e}")
    
    # 错误的产品
    try:
        product2 = Product(
            name="",           # 空名称
            price=-100,        # 负价格
            category="无效类别"  # 无效类别
        )
        print(f"✅ 产品创建成功: {product2}")
    except ValidationError as e:
        print(f"❌ 验证失败:")
        for error in e.errors():
            print(f"   - {error['loc'][0]}: {error['msg']}")

# ============================================================================
# 5. JSON 序列化和反序列化
# ============================================================================

def demo_json_serialization():
    """演示JSON序列化"""
    print("\n=== JSON 序列化演示 ===")
    
    # 创建对象
    user = User(
        id=1,
        username="testuser",
        email="<EMAIL>",
        address=Address(street="测试街道", city="测试城市"),
        tags=["tag1", "tag2"]
    )
    
    # 转换为字典
    user_dict = user.model_dump()
    print("📄 转换为字典:")
    print(json.dumps(user_dict, indent=2, ensure_ascii=False, default=str))
    
    # 转换为JSON字符串
    user_json = user.model_dump_json()
    print("\n📄 转换为JSON字符串:")
    print(user_json)
    
    # 从字典创建对象
    new_user = User(**user_dict)
    print(f"\n✅ 从字典重建对象: {new_user.username}")
    
    # 从JSON字符串创建对象
    json_data = '{"id": 2, "username": "jsonuser", "email": "<EMAIL>"}'
    user_from_json = User.model_validate_json(json_data)
    print(f"✅ 从JSON重建对象: {user_from_json.username}")

# ============================================================================
# 6. 在结构化输出中的应用
# ============================================================================

class AITaskResponse(BaseModel):
    """AI任务响应模型 - 结构化输出的典型应用"""
    task_type: str = Field(description="任务类型")
    result: str = Field(description="任务结果")
    confidence: float = Field(description="置信度", ge=0, le=1)
    steps: List[str] = Field(description="执行步骤", min_items=1)
    metadata: Optional[Dict[str, str]] = Field(None, description="元数据")
    
    @validator('task_type')
    def validate_task_type(cls, v):
        valid_types = ['分析', '生成', '翻译', '总结', '问答']
        if v not in valid_types:
            raise ValueError(f'任务类型必须是: {valid_types}')
        return v

def demo_structured_output_application():
    """演示在结构化输出中的应用"""
    print("\n=== 结构化输出应用演示 ===")
    
    # 模拟AI返回的结构化数据
    ai_response_data = {
        "task_type": "分析",
        "result": "根据数据分析，用户主要关注价格和质量两个因素",
        "confidence": 0.85,
        "steps": [
            "收集用户反馈数据",
            "进行情感分析",
            "提取关键因素",
            "生成分析报告"
        ],
        "metadata": {
            "model": "gpt-4",
            "version": "1.0"
        }
    }
    
    try:
        # 验证AI返回的数据
        ai_response = AITaskResponse(**ai_response_data)
        print("✅ AI响应验证成功:")
        print(f"   任务类型: {ai_response.task_type}")
        print(f"   结果: {ai_response.result}")
        print(f"   置信度: {ai_response.confidence}")
        print(f"   步骤数量: {len(ai_response.steps)}")
        
        # 生成JSON Schema (用于告诉AI应该返回什么格式)
        schema = AITaskResponse.model_json_schema()
        print(f"\n📋 生成的JSON Schema (前3个属性):")
        for key in list(schema['properties'].keys())[:3]:
            prop = schema['properties'][key]
            print(f"   {key}: {prop.get('type', 'unknown')} - {prop.get('description', '')}")
            
    except ValidationError as e:
        print(f"❌ AI响应验证失败: {e}")

# ============================================================================
# 主演示函数
# ============================================================================

def main():
    """主演示函数"""
    print("🚀 Pydantic 模型详细演示")
    print("=" * 60)
    
    demo_basic_model()
    demo_field_constraints()
    demo_complex_types()
    demo_custom_validators()
    demo_json_serialization()
    demo_structured_output_application()
    
    print("\n" + "=" * 60)
    print("📝 Pydantic 总结:")
    print("1. 🏗️  定义数据结构 - 使用类和类型注解")
    print("2. ✅ 自动验证 - 确保数据符合规则")
    print("3. 🔄 类型转换 - 自动处理类型转换")
    print("4. 📋 生成文档 - 自动生成JSON Schema")
    print("5. 🔒 类型安全 - 提供编译时和运行时检查")
    print("6. 🤖 AI集成 - 完美适配结构化输出需求")

if __name__ == "__main__":
    main()
